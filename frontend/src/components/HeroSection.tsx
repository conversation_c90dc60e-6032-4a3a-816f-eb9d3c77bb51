import React, { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from 'lucide-react';

import img1 from '@/assets/images/img1.png';
import img2 from '@/assets/images/img2.png';
import img3 from '@/assets/images/img3.jpg';
import img4 from '@/assets/images/img4.png';
import img5 from '@/assets/images/img5.jpg';

const HeroSection = () => {
  const slides = [img1, img2, img3, img4, img5];
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showContent, setShowContent] = useState(true);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
  };

  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowContent(false);
    }, 5000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <section className="relative text-white py-16 md:py-24 overflow-hidden min-h-[600px]">
      {/* Image Slider */}
      <div className="absolute inset-0 z-0">
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={slide}
              alt={`Slide ${index + 1}`}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      <div className="absolute inset-y-0 left-0 flex items-center z-20">
        <button
          onClick={prevSlide}
          className="bg-black/30 hover:bg-black/50 text-white p-2 rounded-r-md transition-colors"
          aria-label="Previous slide"
        >
          <ChevronLeft size={24} />
        </button>
      </div>
      <div className="absolute inset-y-0 right-0 flex items-center z-20">
        <button
          onClick={nextSlide}
          className="bg-black/30 hover:bg-black/50 text-white p-2 rounded-l-md transition-colors"
          aria-label="Next slide"
        >
          <ChevronRight size={24} />
        </button>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-20">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-colors ${
              index === currentSlide ? 'bg-white' : 'bg-white/50'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Content Section with Timeout */}
      {showContent && (
        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col items-center">
            <div className="w-full max-w-3xl space-y-6 mb-10 relative p-8 rounded-xl">
              {/* Semi-transparent backdrop for better text readability */}
              <div className="absolute inset-0 bg-black/30 backdrop-blur-sm rounded-xl -z-10"></div>

              <h1 className="text-4xl md:text-5xl font-bold leading-tight animate-slide-in text-shadow-strong text-center">
                GyaanRaksha Samyog
              </h1>
              <p className="sanskrit-quote text-xl md:text-2xl italic font-medium text-gyaan-gold animate-slide-in text-shadow-strong text-center" style={{ animationDelay: "0.2s" }}>
                ज्ञानेन रक्षा, विकसित भारतम्
              </p>
              <p className="text-lg md:text-xl font-medium animate-slide-in text-shadow-strong text-center" style={{ animationDelay: "0.3s" }}>
                "Empowering Protection Through Knowledge"
              </p>
              <p className="text-base text-white animate-slide-in text-shadow text-center" style={{ animationDelay: "0.4s" }}>
                Your digital gateway to accredited training courses from India's premier
                State Police, Central Police Organisations, and Central Armed Police Forces institutes.
              </p>
              <div className="pt-4 flex flex-wrap gap-4 animate-slide-in justify-center" style={{ animationDelay: "0.5s" }}>
                <NavLink to="/register">
                  <Button variant="maroon" className="px-8 py-6">
                    Register Now
                  </Button>
                </NavLink>
                <NavLink to="/login">
                  <Button variant="outline" glow="gold" className="border-gyaan-gold bg-gyaan-gold/20 text-white hover:bg-gyaan-gold/40 px-8 py-6">
                    Candidate Login
                  </Button>
                </NavLink>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default HeroSection;
