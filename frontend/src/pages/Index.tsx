import React from 'react';
import { NavLink } from 'react-router-dom';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import HeroSection from '@/components/HeroSection';
import StatsDashboard from '@/components/StatsDashboard';
import PartnerLogos from '@/components/PartnerLogos';

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col bg-white text-gray-900">
      <RRUHeader />
      <Header />

      <main className="flex-grow">
        <HeroSection />

        {/* About the Portal */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-5xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gyaan-navy">
                  About GyaanRaksha Samyog
                </h2>
                <div className="w-24 h-1 bg-gyaan-gold mx-auto mt-4"></div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
                <div className="bg-white border border-gray-200 p-8 rounded-lg shadow-lg">
                  <h3 className="text-2xl font-bold mb-4 text-gyaan-navy">
                    Our Mission
                  </h3>
                  <p className="text-lg mb-4 text-gray-700">
                    <strong>GyaanRaksha Samyog</strong> is your digital gateway
                    to accredited training courses from India's premier State
                    Police, Central Police Organisations, and Central Armed
                    Police Forces institutes.
                  </p>
                  <p className="text-lg mb-4 text-gray-700">
                    Aligned with <strong>Viksit Bharat</strong>, we empower
                    individuals to build a secure, progressive India through
                    expert-led training in policing, security, and governance.
                  </p>
                  <blockquote className="mt-6 text-xl italic border-l-4 border-gyaan-gold pl-4 text-gyaan-navy">
                    ज्ञानेन रक्षा, विकसित भारतम्
                  </blockquote>
                </div>

                <div className="space-y-6">
                  <div className="p-6 border border-gray-200 bg-white shadow-lg rounded-lg">
                    <h3 className="text-xl font-semibold text-gyaan-navy mb-2">
                      Gyaan (Knowledge)
                    </h3>
                    <p className="text-gray-700">
                      Access specialized knowledge and training from India’s top
                      security institutions to empower yourself in your field.
                    </p>
                  </div>

                  <div className="p-6 border border-gray-200 bg-white shadow-lg rounded-lg">
                    <h3 className="text-xl font-semibold text-gyaan-navy mb-2">
                      Raksha (Protection)
                    </h3>
                    <p className="text-gray-700">
                      Strengthen India’s security infrastructure by becoming a
                      trained protection professional.
                    </p>
                  </div>

                  <div className="p-6 border border-gray-200 bg-white shadow-lg rounded-lg">
                    <h3 className="text-xl font-semibold text-gyaan-navy mb-2">
                      Samyog (Connection)
                    </h3>
                    <p className="text-gray-700">
                      Connect with top institutes and programs accredited by
                      Rashtriya Raksha University for professional excellence.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-12 text-center">
                <p className="text-lg max-w-3xl mx-auto mb-6 text-gray-600">
                  Explore courses, apply with ease, and begin your journey to
                  safeguard the nation through our platform.
                </p>
                <NavLink
                  to="/courses"
                  className="inline-block bg-gyaan-navy text-white px-8 py-3 rounded-lg hover:bg-gyaan-navy/90 transition"
                >
                  Discover Our Courses
                </NavLink>
              </div>
            </div>
          </div>
        </section>

        <StatsDashboard />

        {/* How It Works */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gyaan-navy">
                How It Works
              </h2>
              <div className="w-24 h-1 bg-gyaan-gold mx-auto mt-4"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  step: '1',
                  title: 'Register & Apply',
                  desc: 'Create your account, Complete your profile & apply for courses from accredited institutes.',
                },
                {
                  step: '2',
                  title: 'Learn & Train',
                  desc: 'Access accredited training content, participate in virtual counseling, and enhance your skills with specialized courses.',
                },
                {
                  step: '3',
                  title: 'Certify & Advance',
                  desc: 'Complete your courses, receive accredited certifications, and advance your career in the field of safety and security.',
                },
              ].map((item, idx) => (
                <div
                  key={idx}
                  className="bg-white border border-gray-200 p-6 rounded-lg text-center shadow-lg"
                >
                  <div className="w-12 h-12 flex items-center justify-center rounded-full bg-gyaan-navy text-white font-bold text-xl mx-auto mb-4">
                    {item.step}
                  </div>
                  <h3 className="text-xl font-semibold text-gyaan-navy mb-2">
                    {item.title}
                  </h3>
                  <p className="text-gray-700">{item.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <PartnerLogos />

        {/* Call to Action */}
        <section className="py-16 bg-gyaan-navy text-white text-center">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Begin Your Journey?
            </h2>
            <p className="text-lg mb-6 max-w-xl mx-auto">
              Join GyaanRaksha Samyog and take the first step toward a secure,
              knowledge-driven future.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <NavLink
                to="/register"
                className="bg-white text-gyaan-navy px-8 py-3 rounded-lg hover:bg-gray-100 transition font-semibold"
              >
                Register Now
              </NavLink>
              <NavLink
                to="/courses"
                className="border-2 border-white px-8 py-3 rounded-lg hover:bg-white/10 transition font-semibold"
              >
                Explore Courses
              </NavLink>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Index;
